<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1557</width>
    <height>717</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>1582</width>
      <height>38</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_11">
     <item>
      <widget class="QWidget" name="widget_2" native="true">
       <property name="styleSheet">
        <string notr="true">background-color: transparent;</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_10">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_9">
          <property name="spacing">
           <number>5</number>
          </property>
          <item>
           <spacer name="horizontalSpacer_11">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>520</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label_channel">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>140</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
             </font>
            </property>
            <property name="text">
             <string>通道：UVC</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_fbl">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>140</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
             </font>
            </property>
            <property name="text">
             <string>分辨率：4K</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_fps">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
             </font>
            </property>
            <property name="text">
             <string>帧率：60</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_BM">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>140</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
             </font>
            </property>
            <property name="text">
             <string>编码：H264</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_ML">
            <property name="minimumSize">
             <size>
              <width>140</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>140</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
             </font>
            </property>
            <property name="text">
             <string>码流：40MB</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_storage">
            <property name="minimumSize">
             <size>
              <width>180</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>180</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
             </font>
            </property>
            <property name="text">
             <string>存储位置：TF卡</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_12">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="recordingIndicatorWidget" native="true">
       <property name="styleSheet">
        <string notr="true">background-color: transparent;</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_13">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>6</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_12">
          <item>
           <widget class="QLabel" name="recordingDotLabel">
            <property name="minimumSize">
             <size>
              <width>20</width>
              <height>20</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>20</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: red;
border-radius: 10px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="recordingTimeLabel">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);
background-color: transparent;</string>
            </property>
            <property name="text">
             <string>00:00:00</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>1241</width>
      <height>792</height>
     </rect>
    </property>
    <layout class="QGridLayout" name="gridLayout_4">
     <item row="1" column="0">
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>171</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="0" column="2">
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>85</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="2" column="3">
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>171</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="1" column="1" rowspan="2" colspan="2">
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <widget class="QStackedWidget" name="stackedWidget">
         <property name="styleSheet">
          <string notr="true">background-color: rgba(119, 118, 123,127);</string>
         </property>
         <property name="currentIndex">
          <number>6</number>
         </property>
         <widget class="QWidget" name="menu">
          <property name="styleSheet">
           <string notr="true">background-color: rgba(119, 118, 123,127);</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_3">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <property name="spacing">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_3">
               <item>
                <widget class="QLabel" name="main_menu">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>60</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>31</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                 </property>
                 <property name="text">
                  <string>主菜单</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_3">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <widget class="Line" name="line">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(119, 118, 123);</string>
               </property>
               <property name="midLineWidth">
                <number>5</number>
               </property>
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_2">
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_2">
                 <item>
                  <widget class="QPushButton" name="Camera">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>图像参数</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                   <property name="autoRepeat">
                    <bool>false</bool>
                   </property>
                   <property name="autoExclusive">
                    <bool>false</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="Recordset">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>录像设置</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="Filemanage">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>文件管理</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="Systemset">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>系统设置</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <spacer name="horizontalSpacer">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_7">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_imageparame">
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_7">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_5">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_14">
                 <property name="sizeConstraint">
                  <enum>QLayout::SetFixedSize</enum>
                 </property>
                 <item>
                  <widget class="QLabel" name="camera">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                   </property>
                   <property name="text">
                    <string>图像参数</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_13">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Expanding</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="Line" name="line_4">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(119, 118, 123);</string>
                 </property>
                 <property name="midLineWidth">
                  <number>5</number>
                 </property>
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_15">
                 <item>
                  <layout class="QVBoxLayout" name="verticalLayout_6">
                   <item>
                    <widget class="QPushButton" name="Camera_UVC">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>UVC</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                     <property name="autoRepeat">
                      <bool>false</bool>
                     </property>
                     <property name="autoExclusive">
                      <bool>false</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="Camera_HDMI">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>HDMI</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_14">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="UVC_imageset">
          <layout class="QVBoxLayout" name="verticalLayout_24">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_16">
             <property name="sizeConstraint">
              <enum>QLayout::SetFixedSize</enum>
             </property>
             <item>
              <widget class="QLabel" name="camera_UVC">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>60</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>60</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>31</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
               </property>
               <property name="text">
                <string>UVC图像参数</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_15">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="Line" name="line_5">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(119, 118, 123);</string>
             </property>
             <property name="midLineWidth">
              <number>5</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QScrollArea" name="scrollArea_UVC">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="lineWidth">
              <number>1</number>
             </property>
             <property name="midLineWidth">
              <number>0</number>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="horizontalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents_2">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>867</width>
                <height>460</height>
               </rect>
              </property>
              <layout class="QGridLayout" name="gridLayout_11">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item row="0" column="0">
                <layout class="QVBoxLayout" name="verticalLayout_23">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_33">
                   <property name="spacing">
                    <number>6</number>
                   </property>
                   <item>
                    <widget class="QPushButton" name="UVC_brightness">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>亮度</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_32">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_UVC_brightness">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>11</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_34">
                   <item>
                    <widget class="QPushButton" name="UVC_contrast">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>对比度</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_33">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_UVC_contrast">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_35">
                   <item>
                    <widget class="QPushButton" name="UVC_saturation">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>饱和度</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_34">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_UVC_saturation">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_36">
                   <item>
                    <widget class="QPushButton" name="UVC_hue">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>色调</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_35">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_UVC_hue">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_37">
                   <item>
                    <widget class="QPushButton" name="UVC_exposure_auto">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>自动曝光</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_36">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QCheckBox" name="checkBox_UVC_exposure">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>是否自动</string>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_38">
                   <item>
                    <widget class="QPushButton" name="UVC_exposure">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>曝光</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_37">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_UVC_exposure">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_39">
                   <item>
                    <widget class="QPushButton" name="UVC_white_auto">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>自动白平衡</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_38">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QCheckBox" name="checkBox_UVC_white">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>是否自动</string>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_40">
                   <item>
                    <widget class="QPushButton" name="UVC_white">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>白平衡</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_39">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox__UVC_white">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="HDMI_imageset">
          <layout class="QVBoxLayout" name="verticalLayout_26">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_17">
             <property name="sizeConstraint">
              <enum>QLayout::SetFixedSize</enum>
             </property>
             <item>
              <widget class="QLabel" name="camera_HDMI">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>60</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>60</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>31</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
               </property>
               <property name="text">
                <string>HDMI图像参数</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_16">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="Line" name="line_6">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(119, 118, 123);</string>
             </property>
             <property name="midLineWidth">
              <number>5</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QScrollArea" name="scrollArea_HDMI">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="lineWidth">
              <number>1</number>
             </property>
             <property name="midLineWidth">
              <number>0</number>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="horizontalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents_3">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>867</width>
                <height>460</height>
               </rect>
              </property>
              <layout class="QGridLayout" name="gridLayout_12">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item row="0" column="0">
                <layout class="QVBoxLayout" name="verticalLayout_25">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_41">
                   <item>
                    <widget class="QPushButton" name="HDMI_brightness">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>亮度</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_40">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_HDMI_brightness">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>11</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_42">
                   <item>
                    <widget class="QPushButton" name="HDMI_contrast">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>对比度</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_41">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_HDMI_contrast">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_43">
                   <item>
                    <widget class="QPushButton" name="HDMI_saturation">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>饱和度</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_42">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_HDMI_saturation">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_44">
                   <item>
                    <widget class="QPushButton" name="HDMI_hue">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>色调</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_43">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_HDMI_hue">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_45">
                   <item>
                    <widget class="QPushButton" name="HDMI_exposure_auto">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>自动曝光</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_44">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QCheckBox" name="checkBox_HDMI_exposure_auto">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>是否自动</string>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_46">
                   <item>
                    <widget class="QPushButton" name="HDMI_exposure">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>曝光</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_45">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_HDMI_exposure">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_47">
                   <item>
                    <widget class="QPushButton" name="HDMI_white_auto">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>自动白平衡</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_46">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QCheckBox" name="checkBox_HDMI_white">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>是否自动</string>
                     </property>
                     <property name="iconSize">
                      <size>
                       <width>30</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_48">
                   <item>
                    <widget class="QPushButton" name="HDMI_white">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>白平衡</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_47">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QSpinBox" name="spinBox_HDMI_white">
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QSpinBox {
    border: 1px solid gray;
    border-radius: 3px;
    padding-right: 20px;  /* 为加号按钮预留空间 */
    padding-left: 20px;   /* 为减号按钮预留空间 */
}

/* 加号按钮（右侧） */
QSpinBox::up-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: right center;  /* 固定在右侧 */
    width: 20px;
    image: url(:/icons/加号.png);  /* 自定义加号图标 */
}

/* 减号按钮（左侧） */
QSpinBox::down-button {
    subcontrol-origin: border;  /* 相对于边框定位 */
    subcontrol-position: left center;  /* 固定在左侧 */
    width: 20px;
    image: url(:/icons/减号.png);  /* 自定义减号图标 */
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_recordset">
          <layout class="QVBoxLayout" name="verticalLayout_12">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_9">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_10">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_18">
                 <property name="sizeConstraint">
                  <enum>QLayout::SetFixedSize</enum>
                 </property>
                 <item>
                  <widget class="QLabel" name="recordset_1">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                   </property>
                   <property name="text">
                    <string>录像设置</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_17">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Expanding</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="Line" name="line_7">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(119, 118, 123);</string>
                 </property>
                 <property name="midLineWidth">
                  <number>5</number>
                 </property>
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_19">
                 <item>
                  <layout class="QVBoxLayout" name="verticalLayout_11">
                   <item>
                    <widget class="QPushButton" name="record_UVC">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>UVC</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                     <property name="autoRepeat">
                      <bool>false</bool>
                     </property>
                     <property name="autoExclusive">
                      <bool>false</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="record_HDMI">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>HDMI</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_18">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_recordset_2">
          <layout class="QVBoxLayout" name="verticalLayout_15">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_20">
             <property name="sizeConstraint">
              <enum>QLayout::SetFixedSize</enum>
             </property>
             <item>
              <widget class="QLabel" name="recordset_2">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>60</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>60</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>31</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
               </property>
               <property name="text">
                <string>录像设置</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_19">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="Line" name="line_8">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(119, 118, 123);</string>
             </property>
             <property name="midLineWidth">
              <number>5</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QScrollArea" name="scrollArea">
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>867</width>
                <height>460</height>
               </rect>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_14">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_21">
                 <item>
                  <widget class="QPushButton" name="record_fbl">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>分辨率</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_20">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Fixed</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_choose_fbl">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                   </property>
                   <property name="text">
                    <string>&lt; 4K &gt;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_22">
                 <item>
                  <widget class="QPushButton" name="record_fps">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>帧率</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_21">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Fixed</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_choose_fps">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                   </property>
                   <property name="text">
                    <string>&lt; 60 &gt;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_23">
                 <item>
                  <widget class="QPushButton" name="record_encode">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>编码方式</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_22">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Fixed</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_choose_encode">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                   </property>
                   <property name="text">
                    <string>&lt; H264 &gt;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_24">
                 <item>
                  <widget class="QPushButton" name="record_quality">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>质量</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_23">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Fixed</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_choose_quality">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                   </property>
                   <property name="text">
                    <string>&lt; 中 &gt;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_25">
                 <item>
                  <widget class="QPushButton" name="record_ml">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>自定义码流</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_24">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Fixed</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_26">
                 <item>
                  <widget class="QPushButton" name="record_storage">
                   <property name="minimumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>220</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                   </property>
                   <property name="text">
                    <string>存储位置</string>
                   </property>
                   <property name="checkable">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_25">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Fixed</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="laebl_choose_storage">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                   </property>
                   <property name="text">
                    <string>&lt; TF卡&gt;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_13">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_27">
                   <item>
                    <widget class="QPushButton" name="start_recordingaudioset">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>录音设置</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_26">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QLabel" name="laebl_choose_audio">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                     </property>
                     <property name="text">
                      <string>&lt;麦克风&gt;</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_28">
                   <item>
                    <widget class="QPushButton" name="start_recordingaudioopen">
                     <property name="minimumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>220</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
background-color: rgba(255, 255, 255,127);
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;
text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>录音开关</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_27">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Fixed</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QLabel" name="laebl_audioopen">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);
border:none;</string>
                     </property>
                     <property name="text">
                      <string>&lt;关&gt;</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_filemanage">
          <layout class="QVBoxLayout" name="verticalLayout_19">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_16">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_17">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_29">
                 <property name="sizeConstraint">
                  <enum>QLayout::SetFixedSize</enum>
                 </property>
                 <item>
                  <widget class="QLabel" name="filemanage">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>31</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                   </property>
                   <property name="text">
                    <string>文件管理</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_28">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Expanding</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="Line" name="line_9">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(119, 118, 123);</string>
                 </property>
                 <property name="midLineWidth">
                  <number>5</number>
                 </property>
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_30">
                 <item>
                  <layout class="QVBoxLayout" name="verticalLayout_18">
                   <item>
                    <widget class="QPushButton" name="file_picture">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>图像文件</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                     <property name="autoRepeat">
                      <bool>false</bool>
                     </property>
                     <property name="autoExclusive">
                      <bool>false</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="file_video">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>50</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>31</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                     </property>
                     <property name="text">
                      <string>视频文件</string>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_29">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_5">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="videofile">
          <layout class="QGridLayout" name="gridLayout_8">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QListView" name="listView_videofile">
             <property name="styleSheet">
              <string notr="true">background-color: rgba(119, 118, 123,127);</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="viewpictures">
          <layout class="QGridLayout" name="gridLayout_9">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QListView" name="listView_viewpictures">
             <property name="styleSheet">
              <string notr="true">background-color: rgba(119, 118, 123,127);</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_systemset">
          <property name="styleSheet">
           <string notr="true">background-color: rgba(119, 118, 123,127);</string>
          </property>
          <widget class="QWidget" name="layoutWidget_11">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>883</width>
             <height>422</height>
            </rect>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_20">
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_21">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_31">
                <property name="sizeConstraint">
                 <enum>QLayout::SetFixedSize</enum>
                </property>
                <item>
                 <widget class="QLabel" name="systemset">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>60</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>60</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>31</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                  </property>
                  <property name="text">
                   <string>系统设置</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_30">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeType">
                   <enum>QSizePolicy::Expanding</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <widget class="Line" name="line_10">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(119, 118, 123);</string>
                </property>
                <property name="midLineWidth">
                 <number>5</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_32">
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout_22">
                  <item>
                   <widget class="QPushButton" name="camera_4">
                    <property name="minimumSize">
                     <size>
                      <width>0</width>
                      <height>50</height>
                     </size>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>31</pointsize>
                     </font>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                    </property>
                    <property name="text">
                     <string>UVC</string>
                    </property>
                    <property name="checkable">
                     <bool>true</bool>
                    </property>
                    <property name="autoRepeat">
                     <bool>false</bool>
                    </property>
                    <property name="autoExclusive">
                     <bool>false</bool>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="custom_4">
                    <property name="minimumSize">
                     <size>
                      <width>0</width>
                      <height>50</height>
                     </size>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>31</pointsize>
                     </font>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	
	background-color: rgba(255, 255, 255,127);
padding-left: 10px;
text-align: left;
border:none;
}
QPushButton{border-radius:1px;
	 border: none;

text-align: left;
	color: rgb(255, 255, 255);
	background-color: rgba(90, 90, 90,0);

}</string>
                    </property>
                    <property name="text">
                     <string>HDMI</string>
                    </property>
                    <property name="checkable">
                     <bool>true</bool>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <spacer name="horizontalSpacer_31">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_6">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </widget>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="help_menu" native="true">
         <property name="styleSheet">
          <string notr="true">background-color: rgba(119, 118, 123,127);</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayout">
            <item>
             <widget class="Line" name="line_2">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(119, 118, 123);</string>
              </property>
              <property name="midLineWidth">
               <number>5</number>
              </property>
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout">
              <item>
               <layout class="QGridLayout" name="gridLayout">
                <item row="1" column="1">
                 <widget class="QLabel" name="Return">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>20</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                  </property>
                  <property name="text">
                   <string>[Menu]返回</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="select">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>20</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                  </property>
                  <property name="text">
                   <string>[⬆ ⬇]选择</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="reset">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>20</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                  </property>
                  <property name="text">
                   <string>[Reset]确定</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QLabel" name="modification">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>20</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);
background-color: rgba(119, 118, 123,0);</string>
                  </property>
                  <property name="text">
                   <string>[⬅ ➡]修改</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <spacer name="horizontalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>582</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
     <item row="3" column="1">
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>85</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
